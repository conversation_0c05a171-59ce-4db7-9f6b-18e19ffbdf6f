package bean

type RouteReqHeader struct {
	TraceId  string `json:"traceId"`
	ProdCode string `json:"prodCode"`
	APPID    string `json:"appId"`
	EnvType  int    `json:"envType"`
}

type RouteAPIRequest struct {
	Header  RouteReqHeader     `json:"header"`
	PayLoad RouteAPIReqPayLoad `json:"payload"`
}

type RouteAPIReqPayLoad struct {
	IDC string `json:"idc"`
}

type RouteAPIResponse struct {
	Header  ResHeader          `json:"header"`
	PayLoad RouteAPIResPayLoad `json:"payload"`
}

type RouteAPIResPayLoad struct {
	Region           string `json:"regionCode"`
	Pvid             int64  `json:"pvid"`
	RegionApiGateway string `json:"regionUrl"`
	ProcessId        string `json:"processId"`
	Token            string `json:"token"`
}
