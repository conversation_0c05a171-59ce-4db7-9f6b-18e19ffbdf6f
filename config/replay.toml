[server]
sub = "lynxiao-replay"
http_port = 50409
bus = "searchAPI"

[metrics]
enable = true
url = "/actuator/prometheus"

[log]
level = "debug"  #debug、error 小写
filename = "./logs/replay.log"
console = false

[elk]
elkAble = true
elogAble = true
logFilePath = "./logs/otlpsdk"
logLevel = "debug"
serviceName = "ifly_search_service_otlp"
kafkaBroker = "kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092,kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092,kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"
kafkaTopic = "lynxiao_flow"
queueSize = 1000
workerNum = 12
block = false

[tlb]
enabled = true
servers  = "**************:30132"
name  = "replay-local-tlb"
maxlics  = 1024
host = ""
port = 50406
heartinterval  = 700
[[tlb.api]]
    name = "lynxiao-flow-routeApi"
    serviceName = "flow-dispatch"
    # path = "http://lynxiao-search-api-hf.xf-yun.com/v1/route"
    path = "http://lyxiao-region-api-hf-internal.xf-yun.com/v1/route"
    # path = "http://***********​:30013/v1/route"
    tricker = 5
    enabled = true
[[tlb.api]]
    name = "lynxiao-flow-searchApi"
    serviceName = "flow-dispatch"
    #path = "http://**************:31111/v1/search"
    #path = "lyxiao-region-api-hf-internal.xf-yun.com/v1/search"
    path = "/v1/search"
    tricker = 5
    enabled = true

[parameter]
# appid = "cc501f15"
prodCode = "Search"
# model = ""
# topK = 2
# processId = "674faed1cf3a5f6939acf486"
# correctStrategy = []
# rewriteStrategy = ["5001"]
# filedName="post_ts"
# startTime=123456
# endTime=123456
# domain=["sina.com.cn"]
# scene=["聚合搜索"]
intent="5001"
#idc="hf"

[authentication]
# appId = "cc501f15"
# appKey = "cc501f15"
# appSecrect = "11E9750C72E849F28FA8ED00"
appKey = "h04xl4yp"
appSecrect = "425303187E1D42BABD23BBF6"
intentBlackList = ["3"]

[Intent]
appKey = "ry819lag"
appSecrect = "8962BC42C6894FF282E0BBA8"
prodCode = "Intent"
topK = 2
processId = ""


[LynxiaoMirror]
AppidMap = {"cc501f15"="0n5d6pdq"}
KVSecrect = {"0n5d6pdq"="2433303BD66D457E84EA9AE5"}
envType = 2
idc = "dx"
prodCode = "HealthySearch"
