package main

import (
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/service"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/tlb"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-index-platform/lynxiao-search-common/metrics"
	slog "git.iflytek.com/RS_DataHub/ifly_search_common/log"
	"github.com/gin-gonic/gin"
)

// func init() {
// 	config.Init()

// 	stage.LoadSegDict()
// }

// 启动http服务
func runHttp(ser *service.MainService) {
	lynxiaoMirror := new(service.LynxiaoMirror)
	router := gin.New()
	router.Use(slog.GinLogger(), slog.GinRecovery(true))

	rg1 := router.Group("/v2")
	{
		rg1.POST("/search", ser.HttpHandler)
	}

	rg3 := router.Group("v1")
	{
		rg3.POST("/search", lynxiaoMirror.HttpHandler)
	}

	port := global.IFLYS_CONFIG.Server.HttpPort
	address := common.HostIp + ":" + strconv.Itoa(port)
	slog.InfoF("Listening and serving HTTP on %s", address)

	srv := &http.Server{
		Addr:    address,
		Handler: router,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			fmt.Println(err)
			slog.FatalF("ListenAndServe: %v", err)
		}
	}()

	m := metrics.NewMetrics(metrics.Options{
		Enable: global.IFLYS_CONFIG.Metrics.Able,
		Url:    global.IFLYS_CONFIG.Metrics.Url,
	}, nil)

	router.Use(m.Middle())

	router.GET("/ping", func(c *gin.Context) {
		m.StatsErrors("/ping", 1001, 1)
		c.JSON(200, gin.H{"message": "pong"})
	})
	//注册metrics的路由
	m.RegisterRouter(router)
}

func main() {
	ser := new(service.MainService)
	// global.IFLYS_CONFIGManager().Init()
	//启动http服务
	go runHttp(ser)

	fmt.Println("init app success...")
	// 创建一个信号通道
	sigs := make(chan os.Signal, 1)
	// 监听指定的信号，例如 SIGINT 和 SIGTERM
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	//服务注册，开始接入流量
	if global.IFLYS_CONFIG.Tlb.Enabled {
		tlb.TLBInstance.RegisterInst()
	}

	fmt.Println("程序正在运行，按 Ctrl+C 或发信号退出")

	// 使用 select 监听信号通道
	for {
		select {
		case sig := <-sigs:
			fmt.Printf("收到信号: %v\n", sig)
			switch sig {
			case syscall.SIGINT, syscall.SIGTERM:
				//执行服务发现注销本实例
				if global.IFLYS_CONFIG.Tlb.Enabled {
					tlb.TLBInstance.DeRegisterInst()
				}
				fmt.Println("注销实例完成...")
				time.Sleep(time.Duration(5) * time.Second)
				os.Exit(0)
			case syscall.SIGHUP:
				fmt.Println("收到 SIGHUP,可以在这里重新加载配置")
			default:
				fmt.Println("收到其他信号", sig)
			}
		}
	}
}
