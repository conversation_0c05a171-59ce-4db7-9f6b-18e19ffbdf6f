package config

import (
	"fmt"
	"os"
	"path"
	"runtime"
	"sync"

	iflysconfig "git.iflytek.com/RS_DataHub/ifly_search_common/config"
	"github.com/mitchellh/mapstructure"
)

var (
	configManager *ConfigManager
	//项目根目录
	ProjectRootPath = getProjectRootPath()

	configOnce sync.Once
)

type ConfigManager struct {
	configCenter          *iflysconfig.ConfigCenter         // 配置中心
	grpcConnectionManager *iflysconfig.GrpcConnctionManager // grpc连接管理(服务发现)
	config                *Config                           // 服务配置
}

type Config struct {
	Server        *serverConfig         `toml:"server" mapstructure:"server"`
	Res           *resConfig            `toml:"res" mapstructure:"res"`
	Log           *logConfig            `toml:"log" mapstructure:"log"`
	Elk           *elkConfig            `toml:"elk" mapstructure:"elk"`
	Common        *commonConfig         `toml:"common" mapstructure:"common"`
	Ase           *AseConfig            `toml:"ase" mapstructure:"ase"`
	Metrics       *MetricsConfig        `toml:"metrics" mapstructure:"metrics"`
	Intervene     *interveneConfig      `toml:"intervene" mapstructure:"intervene"`
	Tlb           *tlbConfig            `toml:"tlb" mapstructure:"tlb"`
	Seg           *segConfig            `toml:"seg" mapstructure:"seg"`
	Params        *paramsConfig         `toml:"parameter" mapstructure:"parameter"`
	Auth          *AuthenticationConfig `toml:"authentication" mapstructure:"authentication"`
	Intent        *IntentConfig         `toml:"intent" mapstructure:"intent"`
	LynxiaoMirror *LynxiaoMirrorConfig  `toml:"LynxiaoMirror" mapstructure:"LynxiaoMirror"`
}

func getProjectRootPath() string {
	_, filename, _, _ := runtime.Caller(0) //0表示当前本行代码在什么位置
	curDir := path.Dir(filename)           //当前文件所在的目录
	return path.Dir(curDir + "/../../")
}

func init() {
	GetServiceConfigManager().Init()
}

// 配置初始化
func (p *ConfigManager) Init() error {
	// 初始化配置中心
	configCenter := iflysconfig.GetConfigCenter()

	p.configCenter = configCenter
	p.grpcConnectionManager = iflysconfig.GetGrpcConnManager()
	configCenter.SetConfigChangeEvent(p.reload)
	if err := p.configCenter.InitWithCmdParams(); err != nil {
		println(err.Error())
		os.Exit(-1)
	}
	p.parseConfig()
	return nil
}

func (p *ConfigManager) reload() {
	fmt.Println("更新配置")
	content, _ := p.configCenter.GetSingleConfig()
	QUbaseConfig := &Config{}
	mapstructure.Decode(content, &QUbaseConfig)
	p.config = QUbaseConfig
	InitElk()
	InitLog()
	// InitMetrics()
	fmt.Println("重新加载流程结束")
}

func GetServiceConfigManager() *ConfigManager {
	configOnce.Do(func() {
		configManager = &ConfigManager{}
	})
	return configManager
}

func GetServiceConfig() *Config {
	return GetServiceConfigManager().config
}

// 获取配置中心
func GetServiceConfigCenter() *iflysconfig.ConfigCenter {
	return GetServiceConfigManager().configCenter
}

// 获取grpc连接管理
func GetGrpcConnectionManager() *iflysconfig.GrpcConnctionManager {
	return GetServiceConfigManager().grpcConnectionManager
}

// 解析配置并初始化各组件
func (p *ConfigManager) parseConfig() error {
	// 远程配置注入
	p.initServiceConfig()

	return nil
}

// 从配置中心获取配置
func (p *ConfigManager) initServiceConfig() {

	// 默认零值
	ServiceConfig := &Config{}
	// 读取配置中心，并设置默认值
	if err := p.configCenter.GetSingleStruct(ServiceConfig); err != nil {
		panic(err) // 读取配置中心失败，直接退出
	}

	p.config = ServiceConfig

	InitElk()
	InitLog()
	InitMetrics()
	InitApi()
}
