package tlb

import (
	"fmt"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-index-platform/lynxiao-search-common/tlb"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-index-platform/lynxiao-search-common/tlb/config"
)

func CallBack(err error) {
	if err != nil {
		println(err.Error())
	}
}

type TLB struct {
	Tlb tlb.Reporter
}

var TLBInstance = new(TLB)

func (t *TLB) RegisterInst() {
	fmt.Println("注册tlb")
	var err error
	TLBInstance.Tlb, err = tlb.NewReporterWithConf(config.TlbConfig{
		Enabled:           global.IFLYS_CONFIG.Tlb.Enabled,
		Servers:           global.IFLYS_CONFIG.Tlb.LbServer,
		Name:              global.IFLYS_CONFIG.Tlb.ServerName,
		Host:              common.If(global.IFLYS_CONFIG.Tlb.Host == "", common.HostIp, global.IFLYS_CONFIG.Tlb.Host),
		Port:              global.IFLYS_CONFIG.Server.HttpPort,
		Maxlics:           global.IFLYS_CONFIG.Tlb.MaxLic,
		HeartbeatInterval: global.IFLYS_CONFIG.Tlb.HeartbeatInterval,
	}, CallBack)
	if err != nil {
		// panic("new tlb reporter failed, err:" + err.Error())
		fmt.Printf("tlb info:%v\n", err.Error())
		return
	}
	err = TLBInstance.Tlb.Register()
	if err != nil {
		panic("tlb register failed, err:" + err.Error())
	}
	fmt.Println(global.IFLYS_CONFIG.Tlb.LbServer, global.IFLYS_CONFIG.Tlb.ServerName, common.HostIp,
		global.IFLYS_CONFIG.Server.HttpPort,
		global.IFLYS_CONFIG.Tlb.MaxLic, "success")

}

func (t *TLB) DeRegisterInst() {
	// report.GetTLBClient().Close()
	TLBInstance.Tlb.UnRegister()
}
