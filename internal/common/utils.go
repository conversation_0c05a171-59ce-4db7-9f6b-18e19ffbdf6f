package common

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"math/big"
	"net"
	"regexp"
	"strconv"
	"strings"

	iflyscommon "git.iflytek.com/RS_DataHub/ifly_search_common"
	"github.com/spf13/cast"
)

var HostIp = getHostIp()

var allpuncReg = regexp.MustCompile("[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~ ·，《。》、？；￥…（）]+")

//"[，_《。》、？；：‘’＂“”【「】」！@￥…（）—,<.>\/\?\;\:\'\"\[\]\{\}\~\`\!\@\#\$\%\^\&\*\(\)\-\=\+ ·]"

// 查询主机名并获取ip
func getHostIp() string {
	ip, err := iflyscommon.Host2Ip("", "")
	if err != nil {
		panic(err)
	}

	return ip
}

func GenerateUUID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}
	return hex.EncodeToString(b)
}

// 取余运算的基数
var remBase *big.Int = new(big.Int).SetUint64(uint64(math.Pow(2, 63)))

func GenerateStrId(text string) string {
	md5Bytes := md5.Sum([]byte(text))      //md5哈希的结果是128bit
	return hex.EncodeToString(md5Bytes[:]) //十六进制编码之后是128/4=32个字符
}

// 通过计算text的MD5Hash值 生成int64类型的id
func GenerateLongId(text string) int64 {
	md5Bytes := md5.Sum([]byte(text))
	idInt := new(big.Int).SetBytes(md5Bytes[:])
	idInt.Rem(idInt, remBase)

	return idInt.Int64()
}

func ToInt64Slice(data, sep string) []int64 {
	slice := strings.Split(data, sep)
	var result []int64
	for _, v := range slice {
		result = append(result, cast.ToInt64(v))
	}
	return result
}

func IntContain(src int, dst []int) bool {
	for _, eachItem := range dst {
		if eachItem == src {
			return true
		}
	}
	return false
}

// func GetIds2Str(src *[]bean.QueryResult) []string {
// 	var ids []string
// 	if src == nil || len(*src) == 0 {
// 		return nil
// 	}

// 	for i := 0; i < len(*src); i++ {
// 		ids = append(ids, strconv.FormatInt((*src)[i].ID, 10))
// 	}

// 	return ids
// }

// func RecallDetailFormat(src *[]bean.RecallDetail) []*iflys_textrecall.RecallDetail {

// 	var dst []*iflys_textrecall.RecallDetail
// 	for _, v := range *src {
// 		tmp := iflys_textrecall.RecallDetail{
// 			Mode:      int32(v.Mode),
// 			SqlString: v.SqlString,
// 			Ids:       v.IDs,
// 			Consumed:  int32(v.Consumed),
// 			Msg:       v.Message,
// 			QueryType: int32(v.QueryType),
// 		}

// 		dst = append(dst, &tmp)
// 	}

// 	return dst
// }

// func Deduplicate(src, dst []bean.QueryResult) []bean.QueryResult {
// 	var ret []bean.QueryResult
// 	var flag bool
// 	if len(src) == 0 {
// 		return dst
// 	}

// 	if len(dst) == 0 {
// 		return nil
// 	}

// 	for _, a := range dst {
// 		for _, b := range src {
// 			if a.ID == b.ID {
// 				flag = true
// 				break
// 			}
// 		}

// 		if !flag {
// 			ret = append(ret, a)
// 		} else {
// 			flag = false
// 		}
// 	}

// 	return ret
// }

func Filter[T any](slice []T, predicate func(index int, item T) bool) []T {
	filtered := make([]T, 0)
	for index, item := range slice {
		if predicate(index, item) {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

func Map[T any, C any](slice []T, mapper func(index int, item T) C) []C {
	mapped := make([]C, len(slice))
	for index, item := range slice {
		mapped[index] = mapper(index, item)
	}
	return mapped
}

func MaxScore[T any](slice []T, score func(i int, a T) float64) T {
	max := slice[0]
	maIndex := 0
	for i, item := range slice {
		if score(i, item) > score(maIndex, max) {
			max = item
			maIndex = i
		}
	}
	return max
}

func MapAndFilter[T any, C any](slice []T, mapper func(index int, item T) C, predicate func(item T) bool) []C {
	mapped := make([]C, len(slice))
	for index, item := range slice {
		if predicate(item) {
			mapped = append(mapped, mapper(index, item))
		}
	}
	return mapped
}

func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

// 去除特殊字符 转小写
func UniformText(text string) string {
	// 使用正则表达式替换字符串
	result := allpuncReg.ReplaceAllString(text, "")

	return strings.ToLower(result)
}

func CheckPort(address string, port int) bool {
	conn, err := net.Dial("tcp", address+":"+strconv.Itoa(port))
	if err != nil {
		fmt.Printf("Port %s is available\n", port)
		return true
	}
	conn.Close()
	fmt.Printf("Port %s is already in use\n", port)
	return false
}

func Interface2S(value interface{}) (result string) {
	if value == nil {
		return ""
	}

	switch value := value.(type) {

	case []byte: // 取缔 []uint8 --> string
		result = string(value)

	case int64:
		result = strconv.FormatInt(value, 10)

	case float64:
		result = strconv.FormatFloat(value, 'f', -1, 64)

	case json.Number:
		result = value.String()

	case string:
		result = value

	default:
		newValue, _ := json.Marshal(value)
		result = string(newValue)
	}

	return
}

func Interface2I64(value interface{}) int64 {
	if value == nil {
		return 0
	}
	var result int64
	var err error
	switch value := value.(type) {

	case int64:
		result = value

	case float64:
		result = int64(value)

	case string:
		result, err = strconv.ParseInt(value, 10, 64)
		if err != nil {
			fmt.Errorf(err.Error())
		}

	default:
	}

	return result
}

// 求并集
func Union(slice1, slice2 []string) []string {
	m := make(map[string]int)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 0 {
			slice1 = append(slice1, v)
		}
	}
	return slice1
}

// 求交集
func Intersect(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

// 求差集 slice1-并集
func Difference(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	inter := Intersect(slice1, slice2)
	for _, v := range inter {
		m[v]++
	}

	for _, value := range slice1 {
		times, _ := m[value]
		if times == 0 {
			nn = append(nn, value)
		}
	}
	return nn
}

// Contains checks if an element is in a collection using generics.
func Contains[T comparable](collection []T, element T) bool {
	for _, item := range collection {
		if item == element {
			return true
		}
	}
	return false
}

// ConvertFullWidthToHalfWidth converts full-width characters to half-width characters.
func ConvertFullWidthToHalfWidth(input string) string {
	output := []rune{}
	for _, r := range input {
		if r >= '！' && r <= '～' { // Full-width ASCII range
			r -= 0xFEE0
		} else if r == '　' { // Full-width space
			r = ' '
		}
		output = append(output, r)
	}
	return string(output)
}
