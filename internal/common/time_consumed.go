package common

import (
	"fmt"
	"time"
)

type TimeConsumedSlice []*TimeConsumed

type TimeConsumed struct {
	Stage     string            `json:"stage"`
	Consumed  time.Duration     `json:"consumed"`
	Sub       TimeConsumedSlice `json:"sub,omitempty"`
	StartTime time.Time         `json:"start_time"`
	EndTime   time.Time         `json:"end_time"`
}

func (tc *TimeConsumed) AppendSub(sub *TimeConsumed) {
	tc.Sub = append(tc.Sub, sub.End())
}

func (tc *TimeConsumed) Append(stage string, stages ...any) func() {
	sub := NewTimeConsumed(stage, stages...)
	return func() {
		tc.AppendSub(sub)
	}
}

func NewTimeConsumed(stage string, stages ...any) *TimeConsumed {
	if len(stages) > 0 {
		stage = fmt.Sprintf(stage, stages...)
	}
	stage += "耗时"
	return &TimeConsumed{
		Stage:     stage,
		StartTime: time.Now(),
	}
}

func (tc *TimeConsumed) End() *TimeConsumed {
	tc.EndTime = time.Now()
	tc.Consumed = time.Duration(tc.EndTime.Sub(tc.StartTime).Milliseconds())

	return tc
}
