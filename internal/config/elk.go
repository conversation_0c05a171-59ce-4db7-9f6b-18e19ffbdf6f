package config

import (
	"fmt"
	"sync"
	"time"

	"git.iflytek.com/AIaaS/otlp/api"
	"git.iflytek.com/AIaaS/otlp/elog"
	slog "git.iflytek.com/RS_DataHub/ifly_search_common/log"
)

var (
	elkManager *ElkManager
	elkOnce    sync.Once
)

type ElkManager struct {
	Elk        map[string]*elog.ELog
	LogChan    chan []string
	Host       string
	sync.Mutex // guards
	loghander  *api.LogHandler
}

func (elk *ElkManager) Init() {
	elk.Elk = make(map[string]*elog.ELog)
	elk.LogChan = make(chan []string, 100000)

	if GetServiceConfig().Elk.ELKAble == true {
		// fmt.Println("ELKAble1:", global.IFLYS_CONFIG().ELK.ELKAble)
		otelConfig := api.NewOtelConfig(
			// sdk log config
			api.WithLogFile(GetServiceConfig().Elk.LogFilePath),
			api.WithLogLevel(GetServiceConfig().Elk.LogLevel),
			api.WithServiceName(GetServiceConfig().Elk.ServiceName),
			api.WithElogAble(GetServiceConfig().Elk.ElogAble),
			api.WithELkAble(GetServiceConfig().Elk.ELKAble),
			api.WithElkKafkaBroker(GetServiceConfig().Elk.KafkaBroker),
			api.WithElkKafkaTopic(GetServiceConfig().Elk.KafkaTopic),
			api.WithElkQueueSize(GetServiceConfig().Elk.QueueSize),
			api.WithElkWorkerNum(GetServiceConfig().Elk.WorkerNum),
			api.WithBlock(GetServiceConfig().Elk.Block),
		)

		finishChan := make(chan struct{})
		go func() {
			// 等待5s，超时则认为初始化失败
			select {
			case <-time.After(5 * time.Second):
				// elkManager.ElkOpen = false
				fmt.Printf("elklog init failed. err: %v\n", "timeout")
			case <-finishChan:
				// elk.elkClientCache = NewLRUCache(100)
				// elk.elkLogChan = make(chan []string, 10000)
				go elk.DoExec()
				fmt.Printf("elklog init success")
			}
		}()
		// otlp bug: 如果kafka地址有误，会panic
		// kafka地址正确，但 是其他集群的，会一直阻塞
		if _, err := api.InitLogHandler(otelConfig); err != nil {
			panic(fmt.Errorf("elklog init failed. err: %v", err.Error()))
		}
		finishChan <- struct{}{}

		// // init logHandler:  init trace/metrics/elog
		// logHandler, err := api.InitLogHandler(otelConfig)
		// fmt.Println("logHandler:", logHandler)
		// //defer logHandler.Fini()
		// if err != nil {
		// 	fmt.Println(err)
		// }
		// go elk.DoExec()
	}

}

func (elk *ElkManager) CreateElkClient(appid, sid, serviceName, host string) {
	if GetServiceConfig().Elk.ELKAble == true {
		elkLogNew := elog.NewELog(appid, sid, serviceName, host, time.Now().String())
		if elkLogNew != nil {
			elk.Lock()
			elk.Elk[sid] = elkLogNew
			elk.Unlock()
		}
	}
}

func (elk *ElkManager) WriteMessage(sid, key string, value string, status string) {
	if GetServiceConfig().Elk.ELKAble == true {
		elk.LogChan <- []string{sid, key, value, status}
	}
}

func (elk *ElkManager) DoExec() {
	for info := range elk.LogChan {
		elk.Lock()
		elklog, ok := elk.Elk[info[0]]
		if ok {
			(*elklog).LogMsg(elog.KV{Name: info[1], Message: info[2]})
			if info[3] == "2" {
				flushError := (*elklog).Flush()
				if flushError != nil {
					fmt.Printf("send elk data failed. err: %s \n", flushError.Error())
				}
				delete(elk.Elk, info[0])
			}
		}
		elk.Unlock()
	}
}

func (elk *ElkManager) FlushMessage(sid string) {
	elk.Lock()
	if elkLog, ok := elk.Elk[sid]; ok {
		// 等待3s，确保日志写入kafka
		// time.Sleep(3 * time.Second)
		flushError := (*elkLog).Flush()
		if flushError != nil {
			slog.ErrorW("send elk data failed", flushError.Error())
		}
	}
	elk.Unlock()
}

func GetElk() *ElkManager {
	elkOnce.Do(func() {
		elkManager = &ElkManager{}
		// elkManager.Init()
	})
	// fmt.Println(elkManager)
	return elkManager
}

func (elk *ElkManager) Finish() {
	if elk.loghander != nil {
		elk.loghander.Fini()
	}
}
