package service

// import (
// 	"encoding/json"
// 	"fmt"

// 	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/bean"
// 	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common/crawler"
// )

// type Crawler interface {
// 	Exec(req interface{}) (interface{}, error)
// }

// type RouteCrawler struct {
// 	url string
// }

// type SearchCrawler struct {
// 	url string
// }

// type CrawlerFactory interface {
// 	CreateCrawler() Crawler
// }

// type RouteCrawlerFactory struct {
// 	url string
// }

// type SearchCrawlerFactory struct {
// 	url string
// }

// func (cr *RouteCrawler) Exec(req *bean.RouteAPIRequest) (*bean.RouteAPIResponse, error) {
// 	data, err := json.Marshal(req)
// 	if err != nil {
// 		fmt.Println("Error marshaling JSON:", err)
// 		return nil, err
// 	}
// 	body := crawler.Post("", data)
// 	var resp *bean.RouteAPIResponse
// 	err = json.Unmarshal(body, &resp)
// 	if err != nil {
// 		fmt.Println("Error unmarshaling JSON:", err)
// 		return nil, err
// 	}
// 	return resp, nil
// }

// func (cs *SearchCrawler) Exec(req *bean.SearchAPIRequest) (*bean.SearchAPIResponse, error) {
// 	data, err := json.Marshal(req)
// 	if err != nil {
// 		fmt.Println("Error marshaling JSON:", err)
// 		return nil, err
// 	}
// 	body := crawler.Post("", data)
// 	var resp *bean.SearchAPIResponse
// 	err = json.Unmarshal(body, &resp)
// 	if err != nil {
// 		fmt.Println("Error unmarshaling JSON:", err)
// 		return nil, err
// 	}
// 	return resp, nil
// }
