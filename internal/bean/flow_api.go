package bean

type FlowRequest struct {
	Header    QueryHeader `json:"header"`
	Parameter QueryParams `json:"parameter"`
}

type FlowReqHeader struct {
	APPID string `json:"appid"`
	UID   string `json:"uid"`
}

type FlowReqParameter struct {
	USID   string `json:"usid"`
	Query  string `json:"search_text"`
	Domain int    `json:"domain"`
}

type FlowResponse struct {
	Code    int                `json:"code"`
	Message string             `json:"message"`
	Sid     string             `json:"sid"`
	Items   []FlowResponseItem `json:"results"`
}

type QueryHeader struct {
	Appid string `json:"appid"`
	UID   string `json:"uid"`
}

type QueryParams struct {
	SearchText     string  `json:"search_text"`
	Domain         int     `json:"domain"`
	USid           string  `json:"usid"`
	UAppId         string  `json:"uappid"`
	RecallNumber   int     `json:"recall_number"`
	IsNegative     bool    `json:"is_negative"`
	TitleWeight    int     `json:"title_weight"`
	SummaryWeight  int     `json:"summary_weight"`
	ContentWeight  int     `json:"content_weight"`
	KeywordWeight  int     `json:"keyword_weight"`
	Page           int     `json:"page"`
	Debug          int     `json:"debug"`
	Nprobe         int     `json:"nprobe"`
	SortMode       int     `json:"sort_mode"`       //排序方式，0 rank 排序 1 精排排序
	AssemblyMode   int     `json:"assembly_mode"`   //返回结果组长方式，0 按照规则排序 1 混合取TOP
	RecallMode     int     `json:"recall_mode"`     //召回方式，0=文本+向量 1=文本 2=向量
	ResultNumber   int     `json:"result_number"`   //返回结果条数
	DocqaLimit     float32 `json:"docqa_limit"`     //docqa 计算的阈值，低于阈值则舍弃掉，不做排序
	PoliticalLimit float32 `json:"political_limit"` //安全阈值
	ResultLimit    float32 `json:"result_limit"`    //最终结果阈值，低于阈值则不对外输出
	Source         int     `json:"source"`          //来源是哪儿。目前区分大模型与网页demo，当前只做打标签使用
	IsTest         bool    `json:"is_test"`
	RoughNumber    int     `json:"rough_number"` //粗排数量，
	//功能控制开关
	RoughAble           int    `json:"rough_able"`             //是否使用粗排 1 表示使用 -1 表示不适用 配置中心配置，配置中心没有，则默认使用
	ScoringAble         int    `json:"scoring_able"`           //是否使用粗排 1 表示使用 -1 表示不适用 配置中心配置，配置中心没有，则默认使用
	HighLightAble       int    `json:"high_light_able"`        //是否使用高亮服务  1 表示使用 -1 表示不适用 配置中心配置，配置中心没有，则默认不使用
	RankAble            int    `json:"rank_able"`              //是否使用rank服务 1 表示使用 -1 表示不适用 配置中心配置，配置中心没有，则默认使用
	HighLightRepSummary int    `json:"high_light_rep_summary"` //是否使用highlight 替换 原始数据库summary 1 表示使用 -1 表示不适用 配置中心配置，配置中心没有，则默认使用
	AnswerModule        string `json:"answer_module"`          //使用高亮模型
	ScoringModule       string `json:"scoring_module"`         //使用什么精排模型
	DocqaLicc           int    `json:"docqa_licc"`             //精排计算支持的引擎路数
	SearchStartTime     int64  `json:"search_start_time"`      //搜索开始时间
	SearchEndTime       int64  `json:"search_end_time"`        //搜索结束时间
	Host                string `json:"host"`

	SearchType string         `json:"field_type"`
	Advanced   map[string]any `json:"advanced"`

	//精准ID
	ExactId             string   `json:"exact_id"`
	SceneClassification []string `json:"sceneClassification"`
	PlugClassification  string   `json:"pluginClassification"`
	DataSource          string   `json:"dataSource"`
}

type FlowResponseItem struct {
	ID            int64   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Title         string  `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`
	Content       string  `protobuf:"bytes,3,opt,name=Content,proto3" json:"Content,omitempty"`
	Summary       string  `protobuf:"bytes,4,opt,name=Summary,proto3" json:"Summary,omitempty"`
	Url           string  `protobuf:"bytes,5,opt,name=Url,proto3" json:"Url,omitempty"`
	ImageLink     string  `protobuf:"bytes,6,opt,name=ImageLink,proto3" json:"ImageLink,omitempty"`
	UpdateTS      int64   `protobuf:"varint,7,opt,name=UpdateTS,proto3" json:"UpdateTS,omitempty"`
	CrawlTS       int64   `protobuf:"varint,8,opt,name=CrawlTS,proto3" json:"CrawlTS,omitempty"`
	IndexTS       int64   `protobuf:"varint,9,opt,name=IndexTS,proto3" json:"IndexTS,omitempty"`
	Host          string  `protobuf:"bytes,10,opt,name=Host,proto3" json:"Host,omitempty"`
	HostRank      int32   `protobuf:"varint,11,opt,name=HostRank,proto3" json:"HostRank,omitempty"`
	PageRank      int32   `protobuf:"varint,12,opt,name=PageRank,proto3" json:"PageRank,omitempty"`
	Source        string  `protobuf:"bytes,13,opt,name=Source,proto3" json:"Source,omitempty"`
	Keywords      string  `protobuf:"bytes,14,opt,name=Keywords,proto3" json:"Keywords,omitempty"`
	UP            float64 `protobuf:"fixed64,15,opt,name=UP,proto3" json:"UP,omitempty"`
	Comment       float64 `protobuf:"fixed64,16,opt,name=Comment,proto3" json:"Comment,omitempty"`
	PV            float64 `protobuf:"fixed64,17,opt,name=PV,proto3" json:"PV,omitempty"`
	Forward       float64 `protobuf:"fixed64,18,opt,name=Forward,proto3" json:"Forward,omitempty"`
	Freq          int64   `protobuf:"varint,19,opt,name=Freq,proto3" json:"Freq,omitempty"`
	Topic         string  `protobuf:"bytes,20,opt,name=Topic,proto3" json:"Topic,omitempty"`
	Category      string  `protobuf:"bytes,21,opt,name=Category,proto3" json:"Category,omitempty"`
	Author        string  `protobuf:"bytes,22,opt,name=Author,proto3" json:"Author,omitempty"`
	Editor        string  `protobuf:"bytes,23,opt,name=Editor,proto3" json:"Editor,omitempty"`
	AuthorRank    int32   `protobuf:"varint,24,opt,name=AuthorRank,proto3" json:"AuthorRank,omitempty"`
	EventNews     bool    `protobuf:"varint,25,opt,name=EventNews,proto3" json:"EventNews,omitempty"`
	ExtraInfo     string  `protobuf:"bytes,26,opt,name=ExtraInfo,proto3" json:"ExtraInfo,omitempty"`
	Favorite      int64   `protobuf:"varint,27,opt,name=Favorite,proto3" json:"Favorite,omitempty"`
	Domain        int32   `protobuf:"varint,28,opt,name=Domain,proto3" json:"Domain,omitempty"`
	PostTS        int64   `protobuf:"varint,29,opt,name=PostTS,proto3" json:"PostTS,omitempty"`
	DBSource      int32   `protobuf:"varint,30,opt,name=DBSource,proto3" json:"DBSource,omitempty"`
	RecallSource  int32   `protobuf:"varint,31,opt,name=RecallSource,proto3" json:"RecallSource,omitempty"`
	ExactHit      int32   `protobuf:"varint,32,opt,name=ExactHit,proto3" json:"ExactHit,omitempty"`
	Timeliness    int32   `protobuf:"varint,33,opt,name=Timeliness,proto3" json:"Timeliness,omitempty"`
	Realm         string  `protobuf:"bytes,34,opt,name=Realm,proto3" json:"Realm,omitempty"`
	Name          string  `protobuf:"bytes,35,opt,name=Name,proto3" json:"Name,omitempty"`
	Inlink        int64   `protobuf:"varint,36,opt,name=Inlink,proto3" json:"Inlink,omitempty"`
	RealmPos      uint32  `protobuf:"varint,37,opt,name=RealmPos,proto3" json:"RealmPos,omitempty"`
	DomainRank    uint32  `protobuf:"varint,38,opt,name=DomainRank,proto3" json:"DomainRank,omitempty"`
	BaiduWeight   uint32  `protobuf:"varint,39,opt,name=BaiduWeight,proto3" json:"BaiduWeight,omitempty"`
	NewsDomain    string  `protobuf:"bytes,40,opt,name=NewsDomain,proto3" json:"NewsDomain,omitempty"`
	SourceInfo    string  `protobuf:"bytes,41,opt,name=SourceInfo,proto3" json:"SourceInfo,omitempty"`
	IsBlackList   bool    `protobuf:"varint,42,opt,name=IsBlackList,proto3" json:"IsBlackList,omitempty"`
	BlackList     string  `protobuf:"bytes,43,opt,name=BlackList,proto3" json:"BlackList,omitempty"`
	ExtraInfos    string  `protobuf:"bytes,44,opt,name=ExtraInfos,proto3" json:"ExtraInfos,omitempty"`
	Score         float64 `protobuf:"fixed64,45,opt,name=Score,proto3" json:"Score,omitempty"`
	RankScore     float64 `protobuf:"fixed64,46,opt,name=RankScore,proto3" json:"RankScore,omitempty"`
	RoughScore    float64 `protobuf:"fixed64,47,opt,name=RoughScore,proto3" json:"RoughScore,omitempty"`
	HighScore     float64 `protobuf:"fixed64,48,opt,name=HighScore,proto3" json:"HighScore,omitempty"`
	HighSummary   string  `protobuf:"bytes,49,opt,name=HighSummary,proto3" json:"HighSummary,omitempty"`
	IsHighSummary bool    `protobuf:"varint,50,opt,name=IsHighSummary,proto3" json:"IsHighSummary,omitempty"`
	Position      string  `protobuf:"bytes,51,opt,name=Position,proto3" json:"Position,omitempty"`
	Collection    string  `protobuf:"bytes,52,opt,name=Collection,proto3" json:"Collection,omitempty"`
	LCS           float32 `protobuf:"fixed32,53,opt,name=LCS,proto3" json:"LCS,omitempty"`
	DomainFreq    int32   `protobuf:"varint,54,opt,name=DomainFreq,proto3" json:"DomainFreq,omitempty"`
}
