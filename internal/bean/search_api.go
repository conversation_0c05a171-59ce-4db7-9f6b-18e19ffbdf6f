package bean

type SearchAPIReqHeader struct {
	TraceId  string `json:"traceId"`
	Token    string `json:"token"`
	ProdCode string `json:"prodCode"`
	APPID    string `json:"appId"`
}
type SearchAPIRequest struct {
	Header    SearchAPIReqHeader  `json:"header"`
	Parameter SearchAPIParameter  `json:"parameter"`
	PayLoad   SearchAPIReqPayLoad `json:"payload"`
}

type SearchAPIParameter struct {
	Id string `json:"id"`
}

type SearchAPIReqPayLoad struct {
	Query  []string `json:"query"`
	Scene  []string `json:"scene"`
	Intent string   `json:"intent"`
	// 其他字段保持不变
	Filter   *Filter `json:"filter,omitempty"` // 改为指针类型
	APPID    string  `json:"appId"`
	FromType string  `json:"fromType"`
	TopK     int     `json:"topk"`
	Plugin   string  `json:"plugin"`
	// RewriteStrategy []string `json:"rewriteStrategy"`
}

type Filter struct {
	FiledName string   `json:"filedName"`
	StartTime int64    `json:"startTime"`
	EndTime   int64    `json:"endTime"`
	Domain    []string `json:"domain"`
}

type SearchAPIResponse struct {
	Header ResHeader `json:"header"`
	// PayLoad SearchAPIResPayLoad `json:"payload"`
}

type SearchAPIResPayLoad struct {
	// Data []string `json:"data"`
}
