package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common/crawler"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/tlb"
	slog "git.iflytek.com/RS_DataHub/ifly_search_common/log"
	"git.iflytek.com/RS_DataHub/ifly_search_common/metrics"
	"github.com/gin-gonic/gin"
)

type LynxiaoMirror struct {
}

func (s *LynxiaoMirror) execRouteApi(req *bean.RouteAPIRequest) (*bean.RouteAPIResponse, error) {

	appid, ok := global.IFLYS_CONFIG.LynxiaoMirror.APPIDMap[req.Header.APPID]
	if !ok {
		slog.ErrorF("找不到appid")
		return nil, fmt.Errorf("找不到appid")
	}
	req.Header.APPID = appid
	apiSecrect, ok := global.KVSecrect[appid]
	if !ok {
		slog.ErrorF("找不到secrect")
		return nil, fmt.Errorf("找不到secrect")
	}
	serviceItem, ok := config.ApiMap["lynxiao-flow-routeApi"]
	if !ok {
		slog.ErrorF("route api未配置")
		return nil, fmt.Errorf("route api未配置")
	}
	var url string
	if strings.Contains(serviceItem.Path, "http://") {
		url = serviceItem.Path
	} else {
		host, err := tlb.TLBInstance.Tlb.GetBestService(context.TODO(), serviceItem.ServiceName)
		if err != nil {
			slog.ErrorF("tlb未能发现rout api服务")
			return nil, fmt.Errorf("tlb未能发现rout api服务")
		}
		url = fmt.Sprintf("http://%s:%d%s", host.Host, host.Port, serviceItem.Path)
	}

	url, err := AssembleAuthUrl(url, "POST", req.Header.APPID, apiSecrect)
	slog.DebugF("routeAPI url:%v", url)
	if err != nil {
		slog.ErrorF("构造认证url失败")
		panic(err.Error())
	}
	data, err := json.Marshal(req)
	if err != nil {
		slog.ErrorF("Error marshaling JSON:", err)
		return nil, err
	}
	err, body := crawler.Post(url, data)
	if err != nil {
		return nil, err
	}
	slog.DebugF("routeAPI请求:%v", string(data))
	slog.DebugF("routeAPI结果:%v", string(body))
	var resp *bean.RouteAPIResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		slog.ErrorF("Error unmarshaling JSON:", err)
		return nil, err
	}
	if resp.Header.Code == 0 && resp.Header.Message == "" {
		slog.ErrorF("鉴权失败")
		return nil, fmt.Errorf("鉴权失败")
	}
	return resp, nil
}

func (s *LynxiaoMirror) execSearchApi(proxyApi string, req *bean.SearchAPIRequest) (*bean.SearchAPIResponse, error) {
	appid, ok := global.IFLYS_CONFIG.LynxiaoMirror.APPIDMap[req.Header.APPID]
	if !ok {
		slog.ErrorF("找不到appid")
		return nil, fmt.Errorf("找不到appid")
	}
	req.Header.APPID = appid
	req.PayLoad.APPID = appid
	apiSecrect, ok := global.KVSecrect[appid]
	if !ok {
		slog.ErrorF("找不到secrect")
		return nil, fmt.Errorf("找不到secrect")
	}
	serviceItem, ok := config.ApiMap["lynxiao-flow-searchApi"]
	if !ok {
		slog.ErrorF("search api未配置")
		return nil, fmt.Errorf("search api未配置")
	}
	var url string
	slog.DebugF("proxyApi:%v\n", proxyApi)
	if strings.Contains(serviceItem.Path, "http://") {
		url = serviceItem.Path
	} else if proxyApi != "" {
		url = proxyApi
	} else {
		host, err := tlb.TLBInstance.Tlb.GetBestService(context.TODO(), serviceItem.ServiceName)
		if err != nil {
			slog.ErrorF("tlb未能发现search api服务")
			return nil, fmt.Errorf("tlb未能发现rout api服务")
		}
		url = fmt.Sprintf("http://%s:%d%s", host.Host, host.Port, serviceItem.Path)
	}
	url, err := AssembleAuthUrl(url, "POST", req.PayLoad.APPID, apiSecrect)
	slog.DebugF("searchAPI url: %v", url)
	data, err := json.Marshal(req)
	slog.DebugF("searchAPI请求:%v", string(data))
	if err != nil {
		slog.ErrorF("searchAPI请求序列化失败:", err)
		return nil, err
	}
	err, body := crawler.Post(url, data)
	if err != nil {
		slog.ErrorF("searchAPI结果:%v", err.Error())
		return nil, err
	}
	slog.DebugF("searchAPI结果:%v", string(body))
	// ELK记录searchAPI请求和结果
	config.GetElk().WriteMessage(req.Header.TraceId, "responseInfo", string(body), "1")
	config.GetElk().WriteMessage(req.Header.TraceId, "requestInfo", string(data), "2")
	var resp *bean.SearchAPIResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		fmt.Println("Error unmarshaling JSON:", err)
		return nil, err
	}
	return resp, nil
}

func (s *LynxiaoMirror) getIntent(req *bean.SearchAPIRequest) (*bean.SearchAPIRequest, error) {
	appid := req.Header.APPID
	scenes := req.PayLoad.Scene
	isBiz := false
	if appid == "b4b7d678" {
		req.PayLoad.Intent = "2"
		return req, nil
	}
	for _, scene := range scenes {
		if strings.Contains(scene, "聚合搜素") {
			isBiz = true
			break
		}
	}
	if appid == "cc501f15" && isBiz {
		req.PayLoad.Intent = "3"
		return req, nil
	} else if appid == "cc501f15" && !isBiz {
		req.PayLoad.Intent = "1"
		return req, nil
	}
	return req, fmt.Errorf("不符合intent策略")
}

func (s *LynxiaoMirror) parse(_ context.Context, req *bean.SearchAPIRequest) (string, *bean.FlowResponse, error) {
	// 调用分流获取token

	// 参数检查
	sid := req.Header.TraceId
	if sid == "" {
		sid = common.GenerateUUID()
	}
	config.GetElk().CreateElkClient("", sid, global.IFLYS_CONFIG.Server.Sub, common.HostIp+":"+strconv.Itoa(global.IFLYS_CONFIG.Server.HttpPort))
	config.GetElk().WriteMessage(sid, "traceId", sid, "1")
	config.GetElk().WriteMessage(sid, "serviceName", global.IFLYS_CONFIG.Server.Sub, "1")
	inUpload, err := json.Marshal(req)
	if err != nil {
		return sid, nil, fmt.Errorf(err.Error() + "route错误")
	}
	config.GetElk().WriteMessage(sid, "flowRequestInfo", string(inUpload), "1")
	// 调用RouteAPI
	serviceItem, ok := config.ApiMap["lynxiao-flow-routeApi"]
	if !ok {
		return sid, nil, err
	}

	intent := req.PayLoad.Intent

	if common.Contains(global.IFLYS_CONFIG.Auth.IntentBlackList, intent) {
		// 将lynxiaoAPI结果转为searchAPI结果
		flow_resp := &bean.FlowResponse{
			Sid:     sid,
			Code:    10001,
			Message: "intent黑名单",
		}
		return sid, flow_resp, fmt.Errorf("intent黑名单")
	}

	var processId string
	var searchProxyApi string
	var token string
	if serviceItem.Enabled {
		route_req := &bean.RouteAPIRequest{
			Header: bean.RouteReqHeader{
				TraceId:  sid,
				ProdCode: global.IFLYS_CONFIG.LynxiaoMirror.ProdCode,
				APPID:    req.Header.APPID,
				EnvType:  global.IFLYS_CONFIG.LynxiaoMirror.EnvType,
			},
			PayLoad: bean.RouteAPIReqPayLoad{
				IDC: global.IFLYS_CONFIG.LynxiaoMirror.IDC,
			},
		}
		route_resp, err := s.execRouteApi(route_req)
		if err != nil || route_resp == nil || route_resp.Header.Code != 0 {
			fmt.Sprintf("route api调用错误")
			return sid, nil, err
		}
		processId = route_resp.PayLoad.ProcessId
		searchProxyApi = route_resp.PayLoad.RegionApiGateway
		token = route_resp.PayLoad.Token
	} else {
		processId = global.IFLYS_CONFIG.Params.ProcessId
	}

	req.Header.Token = token
	req.Parameter.Id = processId
	req.Header.ProdCode = global.IFLYS_CONFIG.LynxiaoMirror.ProdCode

	// 发送请求
	search_resp, err := s.execSearchApi(searchProxyApi, req)
	if err != nil || search_resp == nil {
		fmt.Sprintf("search api调用错误")
	}

	// 将lynxiaoAPI结果转为searchAPI结果
	flow_resp := &bean.FlowResponse{
		Sid:     sid,
		Code:    search_resp.Header.Code,
		Message: search_resp.Header.Message,
	}
	return sid, flow_resp, nil
}

func (s *LynxiaoMirror) HttpHandler(c *gin.Context) {
	tc := common.NewTimeConsumed("执行镜像搜索全流程")
	if global.IFLYS_CONFIG.Tlb.Enabled {
		tlb.TLBInstance.Tlb.AddUsedLic(1)
		defer tlb.TLBInstance.Tlb.DescUsedLic(1)
	}

	metrics.GetTargetLabelValue().SetType(metrics.GAUGE).SetEndpoint("/v1/search").SetSrv("concurrent_num").SetValue(1).Flush()
	in := &bean.SearchAPIRequest{}
	var resp = &bean.FlowResponse{}
	var sid string
	defer func() {
		tc.End()
		outUpload, _ := json.Marshal(resp)

		config.GetElk().WriteMessage(sid, "flowResponseInfo", string(outUpload), "2")
		metrics.GetTargetLabelValue().SetType(metrics.COUNTER).SetEndpoint("/v1/search").SetCode(strconv.FormatInt(int64(resp.Code), 10)).SetSrv("req_count").SetValue(1).Flush()

		metrics.GetTargetLabelValue().SetType(metrics.HISTOGRAM).SetEndpoint("/v1/search").SetCode(strconv.FormatInt(int64(resp.Code), 10)).SetTable("all").SetSrv("total").SetValue(float64(tc.Consumed)).Flush()
		metrics.GetTargetLabelValue().SetType(metrics.GAUGE).SetEndpoint("/v1/search").SetSrv("concurrent_num").SetValue(-1).Flush()
	}()

	if err := c.BindJSON(in); err != nil {
		config.GetElk().CreateElkClient("", "req_error", global.IFLYS_CONFIG.Server.Sub, common.HostIp+":"+strconv.Itoa(global.IFLYS_CONFIG.Server.HttpPort))
		resp = &bean.FlowResponse{
			Code:    11221,
			Message: "请求解析错误:" + err.Error(),
		}
		outUpload, _ := json.Marshal(resp)

		config.GetElk().WriteMessage("req_error", "flowResponseInfo", string(outUpload), "2")
		config.GetElk().WriteMessage(sid, "code", strconv.FormatInt(int64(resp.Code), 10), "1")
		c.JSON(http.StatusOK, resp)
		defer config.GetElk().FlushMessage("req_error")
	} else {

		inUpload, _ := json.Marshal(in)
		config.GetElk().WriteMessage(sid, "flowRequestInfo", string(inUpload), "1")
		sid, resp, err = s.parse(c, in)
		if err != nil {
			if resp != nil {
				resp = &bean.FlowResponse{
					Sid:     sid,
					Code:    resp.Code,
					Message: err.Error(),
				}
			} else {
				resp = &bean.FlowResponse{
					Sid:     sid,
					Code:    10111,
					Message: err.Error(),
				}
			}

		}
		if resp == nil && err == nil {
			resp = &bean.FlowResponse{
				Sid:     sid,
				Code:    10110,
				Message: "鉴权失败",
			}
		} else if resp != nil && err == nil {
			resp = &bean.FlowResponse{
				Sid:     sid,
				Code:    resp.Code,
				Message: "success",
			}

		}
		config.GetElk().WriteMessage(sid, "code", strconv.FormatInt(int64(resp.Code), 10), "1")
		c.JSON(http.StatusOK, resp)
		defer config.GetElk().FlushMessage(resp.Sid)
	}
}
