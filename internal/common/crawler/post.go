package crawler

import (
	"bytes"
	"io/ioutil"
	"net/http"
)

func Post(url string, data []byte) (error, []byte) {
	// 创建一个新的POST请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return err, nil
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err, nil
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, nil
	}
	return nil, body
}
