package config

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common/maputil"
	slog "git.iflytek.com/RS_DataHub/ifly_search_common/log"
	"git.iflytek.com/RS_DataHub/ifly_search_common/metrics"
)

type commonConfig struct {
}

type AseConfig struct {
	AppID     string `toml:"appid" mapstructure:"appid"`
	ApiKey    string `toml:"apikey" mapstructure:"apikey"`
	ApiSecret string `toml:"apisecret" mapstructure:"apisecret"`
	Url       string `toml:"url" mapstructure:"url"`
	Timeout   int    `toml:"timeout",mapstructure:"timeout",default:"500"`
}

type elkConfig struct {
	ELKAble     bool   `json:"elkAble" mapstructure:"elkAble" default:"false"`
	ElogAble    bool   `json:"elogAble" mapstructure:"elogAble" default:"false"`
	LogFilePath string `json:"logFilePath" mapstructure:"logFilePath" default:"./logs/otlpsdk"`
	LogLevel    string `json:"logLevel" mapstructure:"logLevel" default:"debug"`
	ServiceName string `json:"serviceName" mapstructure:"serviceName" default:"iflys_qubaike_otlp"`
	KafkaBroker string `json:"kafkaBroker" mapstructure:"kafkaBroker" default:"kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"`
	KafkaTopic  string `json:"kafkaTopic" mapstructure:"kafkaTopic" default:"search_otlp"`
	QueueSize   int    `json:"queueSize" mapstructure:"queueSize" default:"10"`
	WorkerNum   int    `json:"workerNum" mapstructure:"resbasedir" default:"5"`
	Block       bool   `json:"block" mapstructure:"block" default:"false" default:"false"`
}

type MetricsConfig struct {
	Able bool   `toml:"able" mapstructure:"able" default:"true"` //默认开，非0都打开
	Port int    `toml:"port" mapstructure:"port" default:"53002"`
	Url  string `toml:"url" mapstructure:"url"`
}

type resConfig struct {
	BaseResDir  string `toml:"resbasedir" mapstructure:"resbasedir"`
	FileCnt     int    `toml:"fileCount" mapstructure:"fileCount" default:"1"`
	ParserCnt   int    `toml:"parseCount" mapstructure:"parseCount default:10"`
	SynonymPath string `toml:"synonymPath" mapstructure:"synonymPath"`
}

type logConfig struct {
	Level    string `toml:"level" mapstructure:"level" default:"error"`
	FileName string `toml:"filename" mapstructure:"filename" default:"./log/qubaike.log"`
	Console  bool   `toml:"console" mapstructure:"console" default:"false" `
}

type serverConfig struct {
	HttpPort int    `toml:"http_port" mapstructure:"http_port" default:"50400"`
	Sub      string `toml:"sub" mapstructure:"sub" default:"qubaike"`
	Bus      string `toml:"bus" mapstructure:"bus" default:"searchAPI"`
}

type interveneConfig struct {
	Enabled       bool   `toml:"enabled" mapstructure:"enabled" default:"true"`
	KafkaBroker   string `toml:"kafka_server" mapstructure:"kafka_server" default:"10.105.180.21:9092,10.105.180.22:9092,10.105.180.24:9092"`
	KafkaTopic    string `toml:"kafka_topic" mapstructure:"kafka_topic" default:"search_qu"`
	MongoUri      string `toml:"mongo_uri" mapstructure:"mongo_uri" default:"******************************************************************************************************************************************************************************************"`
	MongoDatabase string `toml:"mongo_database" mapstructure:"mongo_database" default:"iflysearch_manager"`
}
type ApiInfo struct {
	Name        string `toml:"name" mapstructure:"name"`
	ServiceName string `toml:"serviceName" mapstructure:"serviceName"`
	Path        string `toml:"path" mapstructure:"path"`
	Tricker     int    `toml:"tricker" mapstructure:"tricker"`
	Enabled     bool   `toml:"enabled" mapstructure:"enabled"`
}

type tlbConfig struct {
	LbServer          string    `toml:"servers" mapstructure:"servers"`
	ServerName        string    `toml:"name" mapstructure:"name"`
	MaxLic            int       `toml:"maxlics" mapstructure:"maxlics" default:"1024"`
	HeartbeatInterval int       `toml:"heartinterval" mapstructure:"heartinterval" default:"700"`
	Host              string    `toml:"host" mapstructure:"host"`
	Port              int       `toml:"port" mapstructure:"port"`
	Enabled           bool      `toml:"enabled" mapstructure:"enabled"`
	Api               []ApiInfo `toml:"api" mapstructure:"api"`
}

type paramsConfig struct {
	APPID           string   `toml:"appid" mapstructure:"appid"`
	ProdCode        string   `toml:"prodCode" mapstructure:"prodCode"`
	TopK            int      `toml:"topK" mapstructure:"topK" default:"1024"`
	Model           string   `toml:"model" mapstructure:"model" default:"700"`
	ProcessId       string   `toml:"processId" mapstructure:"processId" default:"700"`
	CorrectStrategy []string `toml:"correctStrategy" mapstructure:"correctStrategy" default:"700"`
	RewriteStrategy []string `toml:"rewriteStrategy" mapstructure:"rewriteStrategy" default:"700"`
	Scene           []string `toml:"scene" mapstructure:"scene" default:"700"`
	Intent          string   `toml:"intent" mapstructure:"intent" default:"700"`
	FiledName       string   `toml:"filedName"`
	StartTime       int64    `toml:"startTime"`
	EndTime         int64    `toml:"endTime"`
	Domain          []string `toml:"domain"`
	IDC             string   `toml:"idc" mapstructure:"idc"  default:"sh"`
	// Filter          map[string]string `toml:"filter" mapstructure:"filter" `
}

type IntentConfig struct {
	AppID      string `toml:"appKey" mapstructure:"appKey"`
	AppSecrect string `toml:"appSecrect" mapstructure:"appSecrect"`
	ProdCode   string `toml:"prodCode" mapstructure:"prodCode"`
	ProcessId  string `toml:"processId" mapstructure:"processId"`
	TopK       int    `toml:"topK" mapstructure:"topK"`
}

type AuthenticationConfig struct {
	AppKey          string   `toml:"appKey" mapstructure:"appKey"`
	AppSecrect      string   `toml:"appSecrect" mapstructure:"appSecrect"`
	IntentBlackList []string `toml:"intentBlackList" mapstructure:"intentBlackList"`
}

type LynxiaoMirrorConfig struct {
	APPIDMap  map[string]string `toml:"AppidMap"`
	KVSecrect map[string]string `toml:"KVSecrect"`
	EnvType   int               `toml:"envType"`
	IDC       string            `toml:"idc"`
	ProdCode  string            `toml:"prodCode"`
}

var ApiMap = make(map[string]ApiInfo, 0)

func InitApi() {
	// 获取API列表
	apiList := GetServiceConfig().Tlb.Api
	for _, apiItem := range apiList {
		ApiMap[apiItem.Name] = apiItem
	}
	slog.DebugF("api列表加载完毕")
}

type segConfig struct {
	Url     string `toml:"url" mapstructure:"url" default:"http://**************:50400/seg/api/v2/texts"`
	Timeout int    `toml:"timeout" mapstructure:"timeout" default:"1"`
	Retry   int    `toml:"lbServer" mapstructure:"lbServer" default:"2"`
}

func InitMetrics() {
	// fmt.Println(GetQubaikeConfig().Metrics.Able)
	if GetServiceConfig().Metrics.Able {
		// 端口占用则服务不启动
		// if isFree := common.CheckPort(common.HostIp, GetServiceConfig().Metrics.Port); !isFree {
		// 	os.Exit(-1)
		// }
		metrics.GetMetrics().InitMetrics(GetServiceConfig().Server.Bus, GetServiceConfig().Server.Sub, strconv.Itoa(GetServiceConfig().Metrics.Port))
		// fmt.Println("metrics is listening on:", GetServiceConfig().Metrics.Port)
	}
}

func InitLog() {
	slog.Logger().Init(GetServiceConfig().Log.Level, GetServiceConfig().Log.FileName, GetServiceConfig().Log.Console)
	fmt.Println("日志初始化")
}

// elk初始化
func InitElk() {
	// elk上报初始化
	elkManager := GetElk()
	// 必须先判断是否开启，否则会报错
	elkManager.Init()
	fmt.Println("ELK初始化完成")
}

func GetASEConfig() *AseConfig {
	return GetServiceConfig().Ase
}

func GetResConfig() *resConfig {
	return GetServiceConfig().Res
}

// func GetSegDictFiles() []string {
// 	segDictDir := fmt.Sprintf("%s/%s", GetResConfig().BaseResDir, "segwords")
// 	files, err := os.ReadDir(segDictDir)
// 	if err != nil {
// 		panic(err)
// 	}

// 	paths := make([]string, len(files))
// 	for index, file := range files {
// 		paths[index] = fmt.Sprintf("%s/%s", segDictDir, file.Name())
// 	}

// 	return paths
// }

func readLines(filePath string, dataCh chan<- string) {
	fmt.Printf("打开文件:%s\n", filePath)
	file, err := os.Open(filePath)
	if err != nil {
		slog.Fatal(err)
		panic(err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)
	for scanner.Scan() {
		line := string(scanner.Bytes())
		dataCh <- line
	}
}

func parseWorker(dataCh <-chan string, resultCh chan<- *SynonymItem, wg *sync.WaitGroup) {
	defer wg.Done()
	// slog.DebugF("解析文件")
	for data := range dataCh {
		detail := new(SynonymItem)
		// slog.DebugF("解析数据：%s", data)
		items := strings.Split(strings.TrimSuffix(data, "\n"), ",")
		detail.Name = items[0]
		detail.Synonym = items[1]
		resultCh <- detail
	}
}

var SynonymDict = maputil.NewConcurrentMap[string, string](0)
var SynonymStaticDict = make(map[string]string, 0)

type SynonymItem struct {
	Name    string
	Synonym string
}

func LoadSynonyms() {
	st := time.Now()
	slog.DebugF("开始加载同义词文件")
	dataCh := make(chan string, 100)
	resultCh := make(chan *SynonymItem, 100)

	configInst := GetServiceConfig()
	fmt.Printf("%v\n", configInst)

	SynonymPath := GetServiceConfig().Res.SynonymPath
	fileCnt := GetServiceConfig().Res.FileCnt
	var wgFileRead sync.WaitGroup
	wgFileRead.Add(fileCnt)

	go func() {
		defer wgFileRead.Done()
		readLines(SynonymPath, dataCh)
	}()

	prCount := GetServiceConfig().Res.ParserCnt
	fmt.Print(prCount)
	var wgParse sync.WaitGroup
	wgParse.Add(prCount)
	for i := 1; i <= prCount; i++ {
		go parseWorker(dataCh, resultCh, &wgParse)
	}

	// 解析结果
	var wgResult sync.WaitGroup
	wgResult.Add(1)
	go func() {
		defer wgResult.Done()

		for result := range resultCh {
			SynonymDict.Set(result.Name, result.Synonym)
		}
		// fmt.Println(jiebaPos)
	}()
	// 等待读取文件的协程完成
	// close(filesCh)
	wgFileRead.Wait()

	//关闭dataCh通道,等待数据解析的协程完成
	close(dataCh)
	wgParse.Wait()
	//关闭resultCh通道,等待初始化entity2ItemsDict对象的协程完成
	close(resultCh)
	wgResult.Wait()
	fmt.Println(SynonymDict)
	slog.DebugF("init synonyms finished,cost time: %s\n", time.Since(st))

}

func LoadSynonymsSingle() {
	st := time.Now()
	slog.DebugF("开始加载同义词文件")

	configInst := GetServiceConfig()
	fmt.Printf("%v\n", configInst)

	SynonymPath := GetServiceConfig().Res.SynonymPath

	fmt.Printf("打开文件:%s\n", SynonymPath)
	file, err := os.Open(SynonymPath)
	if err != nil {
		slog.Fatal(err)
		panic(err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)
	for scanner.Scan() {
		line := string(scanner.Bytes())
		// slog.DebugF("解析数据：%s", data)
		items := strings.Split(strings.TrimSuffix(line, "\n"), ",")
		SynonymStaticDict[items[0]] = items[1]
	}

	// fmt.Println(SynonymStaticDict)
	slog.DebugF("init synonyms finished,cost time: %s\n", time.Since(st))

}
