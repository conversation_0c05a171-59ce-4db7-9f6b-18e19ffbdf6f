package bean

import "go.mongodb.org/mongo-driver/bson"

type IntentRequest struct {
	Sid   string   `json:"sid"`
	Texts []string `json:"texts"`
}

type IntentResponse struct {
	Sid     string `json:sid""`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Result  bson.M `json:"result"`
}

type LynxiaoIntentHeader struct {
	TraceId  string `json:"traceId"`
	AppId    string `json:"appId"`
	ProdCode string `json:"prodCode"`
	Token    string `json:"token"`
}

type LynxiaoIntentParameter struct {
	Id string `json:"id"`
}

type LynxiaoIntentPayLoad struct {
	Query []string `json:"query"`
	TopK  int      `json:"topk"`
}

type LynxiaoIntentRequest struct {
	Header    LynxiaoIntentHeader    `json:"header"`
	Parameter LynxiaoIntentParameter `json:"parameter"`
	PayLoad   LynxiaoIntentPayLoad   `json:"payload"`
}

type LynxiaoIntentResHeader struct {
	Code    int    `json:"code"`
	TraceId string `json:"traceId"`
}

type LynxiaoIntentResPayload struct {
	// Result []LynxiaoIntentResult `json:"result"`
	Result []bson.M `json:"result"`
}

type LynxiaoIntentItem struct {
	Label string  `json:"label"`
	Score float64 `json:"score"`
}

type LynxiaoIntentResult struct {
	Query string              `json:"query"`
	Items []LynxiaoIntentItem `json:"data"`
}

type LynxiaoIntentResponse struct {
	Header  LynxiaoIntentResHeader `json:"header"`
	PayLoad bson.M                 `json:"payload"`
}

// type LynxiaoIntentResults struct {
// 	Result []LynxiaoIntentResult
// }
