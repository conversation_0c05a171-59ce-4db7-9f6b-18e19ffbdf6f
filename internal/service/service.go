package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/common/crawler"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-replay/internal/tlb"
	slog "git.iflytek.com/RS_DataHub/ifly_search_common/log"
	"git.iflytek.com/RS_DataHub/ifly_search_common/metrics"
	"github.com/gin-gonic/gin"
)

type MainService struct {
}

func (s *MainService) execRouteApi(req *bean.RouteAPIRequest) (*bean.RouteAPIResponse, error) {
	apiSecrect, ok := global.KVSecrect[req.Header.APPID]
	if !ok {
		return nil, fmt.Errorf("找不到secrect")
	}
	serviceItem, ok := config.ApiMap["lynxiao-flow-routeApi"]
	if !ok {
		return nil, fmt.Errorf("route api未配置")
	}
	var url string
	if strings.Contains(serviceItem.Path, "http://") {
		url = serviceItem.Path
	} else {
		host, err := tlb.TLBInstance.Tlb.GetBestService(context.TODO(), serviceItem.ServiceName)
		if err != nil {
			return nil, fmt.Errorf("tlb未能发现rout api服务")
		}
		url = fmt.Sprintf("http://%s:%d%s", host.Host, host.Port, serviceItem.Path)
	}

	url, err := AssembleAuthUrl(url, "POST", req.Header.APPID, apiSecrect)
	slog.DebugF("routeAPI url:%v", url)
	if err != nil {
		slog.ErrorF("构造认证url失败")
		panic(err.Error())
	}
	data, err := json.Marshal(req)
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return nil, err
	}
	err, body := crawler.Post(url, data)
	if err != nil {
		return nil, err
	}
	slog.DebugF("routeAPI请求:%v", string(data))
	slog.DebugF("routeAPI结果:%v", string(body))
	var resp *bean.RouteAPIResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		fmt.Println("Error unmarshaling JSON:", err)
		return nil, err
	}
	if resp.Header.Code == 0 && resp.Header.Message == "" {
		return nil, fmt.Errorf("鉴权失败")
	}
	return resp, nil
}

func (s *MainService) execSearchApi(proxyApi string, req *bean.SearchAPIRequest) (*bean.SearchAPIResponse, error) {
	apiSecrect, ok := global.KVSecrect[req.PayLoad.APPID]
	if !ok {
		return nil, fmt.Errorf("找不到secrect")
	}
	serviceItem, ok := config.ApiMap["lynxiao-flow-searchApi"]
	if !ok {
		return nil, fmt.Errorf("search api未配置")
	}
	var url string
	slog.DebugF("proxyApi:%v\n", proxyApi)
	if strings.Contains(serviceItem.Path, "http://") {
		url = serviceItem.Path
	} else if proxyApi != "" {
		// url = proxyApi + serviceItem.Path
		url = proxyApi
	} else {
		host, err := tlb.TLBInstance.Tlb.GetBestService(context.TODO(), serviceItem.ServiceName)
		if err != nil {
			return nil, fmt.Errorf("tlb未能发现rout api服务")
		}
		url = fmt.Sprintf("http://%s:%d%s", host.Host, host.Port, serviceItem.Path)
	}
	url, err := AssembleAuthUrl(url, "POST", req.PayLoad.APPID, apiSecrect)
	slog.DebugF("searchAPI url: %v", url)
	data, err := json.Marshal(req)
	slog.DebugF("searchAPI请求:%v", string(data))
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return nil, err
	}
	err, body := crawler.Post(url, data)
	if err != nil {
		slog.DebugF("searchAPI结果:%v", err.Error())
		return nil, err
	}
	slog.DebugF("searchAPI结果:%v", string(body))
	// ELK记录searchAPI请求和结果
	config.GetElk().WriteMessage(req.Header.TraceId, "responseInfo", string(body), "1")
	config.GetElk().WriteMessage(req.Header.TraceId, "requestInfo", string(data), "2")
	var resp *bean.SearchAPIResponse
	err = json.Unmarshal(body, &resp)
	if err != nil {
		fmt.Println("Error unmarshaling JSON:", err)
		return nil, err
	}
	return resp, nil
}

func (s *MainService) parse(_ context.Context, req *bean.FlowRequest) (string, *bean.FlowResponse, error) {
	// 参数检查
	sid := req.Parameter.USid
	if sid == "" {
		sid = common.GenerateUUID()
	}
	config.GetElk().CreateElkClient("", sid, global.IFLYS_CONFIG.Server.Sub, common.HostIp+":"+strconv.Itoa(global.IFLYS_CONFIG.Server.HttpPort))
	config.GetElk().WriteMessage(sid, "traceId", sid, "1")
	config.GetElk().WriteMessage(sid, "serviceName", global.IFLYS_CONFIG.Server.Sub, "1")
	inUpload, err := json.Marshal(req)
	if err != nil {
		return sid, nil, fmt.Errorf(err.Error() + "route错误")
	}
	config.GetElk().WriteMessage(sid, "flowRequestInfo", string(inUpload), "1")
	// 调用RouteAPI
	serviceItem, ok := config.ApiMap["lynxiao-flow-routeApi"]
	if !ok {
		return sid, nil, err
	}
	var processId string
	var searchProxyApi string
	var token string
	if serviceItem.Enabled {
		route_req := &bean.RouteAPIRequest{
			Header: bean.RouteReqHeader{
				TraceId:  sid,
				ProdCode: global.IFLYS_CONFIG.Params.ProdCode,
				APPID:    req.Header.Appid,
			},
			PayLoad: bean.RouteAPIReqPayLoad{
				IDC: global.IFLYS_CONFIG.Params.IDC,
			},
		}
		route_resp, err := s.execRouteApi(route_req)
		if err != nil || route_resp == nil || route_resp.Header.Code != 0 {
			fmt.Sprintf("route api调用错误")
			return sid, nil, err
		}
		processId = route_resp.PayLoad.ProcessId
		searchProxyApi = route_resp.PayLoad.RegionApiGateway
		token = route_resp.PayLoad.Token
	} else {
		processId = global.IFLYS_CONFIG.Params.ProcessId
	}

	// 参数转换，searchAPI请求转为lynxiaoAPI请求

	search_req := &bean.SearchAPIRequest{
		Header: bean.SearchAPIReqHeader{
			TraceId:  sid,
			Token:    token,
			ProdCode: global.IFLYS_CONFIG.Params.ProdCode,
			APPID:    req.Header.Appid,
		},
		Parameter: bean.SearchAPIParameter{
			Id: processId,
		},
		PayLoad: bean.SearchAPIReqPayLoad{
			Query: []string{req.Parameter.SearchText},
			Scene: req.Parameter.SceneClassification,
			// Intent: req.Header.Domain,
			APPID: req.Header.Appid,
		},
	}

	// 发送请求
	search_resp, err := s.execSearchApi(searchProxyApi, search_req)
	if err != nil || search_resp == nil {
		fmt.Sprintf("search api调用错误")
	}

	// 将lynxiaoAPI结果转为searchAPI结果
	flow_resp := &bean.FlowResponse{
		Sid:     sid,
		Code:    search_resp.Header.Code,
		Message: search_resp.Header.Message,
	}

	// outUpload, err := json.Marshal(req)
	// if err != nil {
	// 	return sid, nil, err
	// }
	// config.GetElk().WriteMessage(sid, "flowResponseInfo", string(outUpload), "1")

	return sid, flow_resp, nil
}

func (s *MainService) HttpHandler(c *gin.Context) {
	tc := common.NewTimeConsumed("执行搜索全流程")
	if global.IFLYS_CONFIG.Tlb.Enabled {
		tlb.TLBInstance.Tlb.AddUsedLic(1)
		defer tlb.TLBInstance.Tlb.DescUsedLic(1)
	}

	metrics.GetTargetLabelValue().SetType(metrics.GAUGE).SetEndpoint("/flow/api/v2").SetSrv("concurrent_num").SetValue(1).Flush()
	in := &bean.FlowRequest{}
	var resp = &bean.FlowResponse{}
	var sid string
	defer func() {
		tc.End()
		outUpload, _ := json.Marshal(resp)

		config.GetElk().WriteMessage(sid, "flowResponseInfo", string(outUpload), "2")
		// c.JSON(http.StatusOK, resp)
		metrics.GetTargetLabelValue().SetType(metrics.COUNTER).SetEndpoint("/qubase/api/v2").SetCode(strconv.FormatInt(int64(resp.Code), 10)).SetSrv("req_count").SetValue(1).Flush()

		metrics.GetTargetLabelValue().SetType(metrics.HISTOGRAM).SetEndpoint("/qubase/api/v2").SetCode(strconv.FormatInt(int64(resp.Code), 10)).SetTable("all").SetSrv("total").SetValue(float64(tc.Consumed)).Flush()
		metrics.GetTargetLabelValue().SetType(metrics.GAUGE).SetEndpoint("/qubase/api/v2").SetSrv("concurrent_num").SetValue(-1).Flush()
	}()

	if err := c.BindJSON(in); err != nil {
		config.GetElk().CreateElkClient("", "req_error", global.IFLYS_CONFIG.Server.Sub, common.HostIp+":"+strconv.Itoa(global.IFLYS_CONFIG.Server.HttpPort))
		resp = &bean.FlowResponse{
			Code:    11221,
			Message: "请求解析错误:" + err.Error(),
		}
		outUpload, _ := json.Marshal(resp)

		config.GetElk().WriteMessage("req_error", "flowResponseInfo", string(outUpload), "2")
		config.GetElk().WriteMessage(sid, "replayTag", "error", "1")
		c.JSON(http.StatusOK, resp)
		defer config.GetElk().FlushMessage("req_error")
	} else {

		inUpload, _ := json.Marshal(in)
		config.GetElk().WriteMessage(sid, "flowRequestInfo", string(inUpload), "1")
		sid, resp, err = s.parse(c, in)
		if err != nil {
			resp = &bean.FlowResponse{
				Sid:     sid,
				Code:    -1,
				Message: err.Error(),
			}
			config.GetElk().WriteMessage(sid, "replayTag", "error", "1")
		}
		if resp == nil && err == nil {
			resp = &bean.FlowResponse{
				Sid:     sid,
				Code:    -1,
				Message: "鉴权失败",
			}
			config.GetElk().WriteMessage(sid, "replayTag", "error", "1")
		} else if resp != nil && err == nil {
			resp = &bean.FlowResponse{
				Sid:     sid,
				Code:    0,
				Message: "success",
			}
			config.GetElk().WriteMessage(sid, "replayTag", "success", "1")
		}

		c.JSON(http.StatusOK, resp)
		defer config.GetElk().FlushMessage(resp.Sid)
	}

}
