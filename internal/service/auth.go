package service

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/url"
	"time"
)

func AssembleAuthUrl(requestUrl, method, apiKey, apiSecret string) (string, error) {
	urlResult, err := url.Parse(requestUrl)
	if err != nil {
		return "", err
	}
	date := time.Now().UTC().Format(time.RFC1123)

	// 创建签名原始字符串
	signatureOrigin := fmt.Sprintf("host: %s\ndate: %s\n%s %s HTTP/1.1",
		urlResult.Hostname(), date, method, urlResult.Path)

	// 计算HMAC SHA-256签名
	h := hmac.New(sha256.New, []byte(apiSecret))
	h.Write([]byte(signatureOrigin))
	signatureSHA := h.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(signatureSHA)

	// 生成授权头
	authorizationOrigin := fmt.Sprintf("hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
		apiKey, "hmac-sha256", "host date request-line", signature)
	authorization := base64.StdEncoding.EncodeToString([]byte(authorizationOrigin))

	values := url.Values{}
	values.Add("host", urlResult.Hostname())
	values.Add("date", date)
	values.Add("authorization", authorization)

	return fmt.Sprintf("%s?%s", requestUrl, values.Encode()), nil

}
